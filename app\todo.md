Use NLP with Sonnet 4 to more accurately assign and split the scenes, include an AI summary of each scene that will be than given to the translator as a extra source and to check if anything's wrong.
do not save automcatically, save based on certain events happening

make the model able to autonomically ask for screenshots at a given line if it has doubts and be able to update their translation based on the screneshots

change iteration cycle to max 5
"🔄 [CorrectionTool] Improvement iteration 3/3"
Also output the requirements for text passing check.

change verifier model to claude 3.5 too?

"1. naturalness: Line 'Co słychać?' sounds too formal for casual anime dialogue between friends (medium)"

This actually sounds good. Better than "Dzień dobry" and less formal, can you adjust the prompt of the model to reflect that?

"4. naturalness: 'zgran<PERSON> paczką' sounds awkward - not typical Polish expression for friend group (medium)"
<PERSON><PERSON><PERSON><PERSON> paczką is in fact tpyical for polish phrases.

