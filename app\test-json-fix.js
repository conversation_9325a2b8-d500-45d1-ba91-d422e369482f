// Quick test for JSON escape fixing functionality
const CorrectionTool = require('./2translate/tools/correction-tool.js');

// Create a test instance
const correctionTool = new CorrectionTool({
  anthropic: null, // We don't need the API for this test
  analysisModel: 'test',
  generationModel: 'test'
});

// Test cases with problematic JSON strings
const testCases = [
  {
    name: 'Unescaped backslash',
    input: '{"reason": "Fix grammar\\punctuation", "line": 1}',
    expected: '{"reason": "Fix grammar\\\\punctuation", "line": 1}'
  },
  {
    name: 'Unescaped newline',
    input: '{"reason": "Fix\nline break", "line": 1}',
    expected: '{"reason": "Fix\\nline break", "line": 1}'
  },
  {
    name: 'Multiple issues',
    input: '{"reason": "Fix\\grammar\nand\ttabs", "line": 1}',
    expected: '{"reason": "Fix\\\\grammar\\nand\\ttabs", "line": 1}'
  }
];

console.log('Testing JSON escape fixing...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`Input:    ${testCase.input}`);
  
  try {
    const fixed = correctionTool.fixJsonEscapeIssues(testCase.input);
    console.log(`Fixed:    ${fixed}`);
    
    // Try to parse the fixed JSON
    const parsed = JSON.parse(fixed);
    console.log(`✅ Successfully parsed: ${JSON.stringify(parsed)}`);
  } catch (error) {
    console.log(`❌ Failed to parse: ${error.message}`);
    
    // Try aggressive fix
    try {
      const aggressiveFixed = correctionTool.aggressiveJsonFix(testCase.input);
      console.log(`Aggressive fix: ${aggressiveFixed}`);
      const parsed = JSON.parse(aggressiveFixed);
      console.log(`✅ Aggressive fix worked: ${JSON.stringify(parsed)}`);
    } catch (secondError) {
      console.log(`❌ Aggressive fix also failed: ${secondError.message}`);
    }
  }
  
  console.log('');
});

console.log('Test completed.');
